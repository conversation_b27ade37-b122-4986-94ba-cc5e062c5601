@use '@styles/variables.scss' as *;

.worktab {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 20px 10px;
  user-select: none;

  .scroll-view {
    width: 100%;
    overflow: hidden;

    .tabs {
      float: left;
      white-space: nowrap;
      background: transparent !important;

      li {
        display: inline-block;
        height: 32px;
        margin-right: 6px;
        font-size: 13px;
        line-height: 32px;
        color: var(--art-text-gray-600);
        text-align: center;
        cursor: pointer;
        background: var(--art-main-bg-color);
        border: 1px solid transparent;
        border-radius: calc(var(--custom-radius) / 2.5 + 2px) !important;
        transition: color 0.1s;

        &:hover {
          color: var(--main-color) !important;
          transition: color 0.2s;
        }

        i {
          position: relative;
          top: 2px;
          padding: 2px;
          margin-left: 5px;
          border-radius: 50%;
          transition: all 0.2s;

          &:hover {
            background: rgb(238 238 238 / 100%);
          }
        }
      }

      .activ-tab {
        color: var(--main-color) !important;
      }
    }
  }

  &.tab-card {
    padding: 4px 20px;
    border-bottom: 1px solid var(--art-border-color);
  }

  &.tab-google {
    padding: 5px 20px 0;
    border-bottom: 1px solid var(--art-border-color);

    .tabs {
      padding-left: 5px;

      li {
        position: relative;
        height: 37px !important;
        line-height: 37px !important;
        border: none !important;
        border-radius: calc(var(--custom-radius) / 2.5 + 4px) !important;

        .line {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 1px;
          height: 16px;
          margin: auto;
          background: var(--art-border-dashed-color);
          transition: opacity 0.15s;
        }

        &:first-child {
          .line {
            opacity: 0;
          }
        }

        $tab-radius-size: 20px;

        &::before,
        &::after {
          position: absolute;
          bottom: 0;
          width: $tab-radius-size;
          height: $tab-radius-size;
          content: '';
          border-radius: 50%;
          box-shadow: 0 0 0 30px transparent;
        }

        &::before {
          left: -$tab-radius-size;
          clip-path: inset(50% -10px 0 50%);
        }

        &::after {
          right: -$tab-radius-size;
          clip-path: inset(50% 50% 0 -10px);
        }

        &:hover {
          box-sizing: border-box;
          color: var(--art-text-gray-600) !important;
          background-color: var(--art-gray-200) !important;
          border-bottom: 1px solid var(--art-main-bg-color) !important;
          border-radius: calc(var(--custom-radius) / 2.5 + 4px) !important;

          .line {
            opacity: 0;
          }
        }

        &:hover + li .line {
          opacity: 0;
        }

        &.activ-tab {
          color: var(--main-color) !important;
          background-color: var(--el-color-primary-light-9) !important;
          border-bottom: 0 !important;
          border-bottom-right-radius: 0 !important;
          border-bottom-left-radius: 0 !important;

          &::before,
          &::after {
            box-shadow: 0 0 0 30px var(--el-color-primary-light-9);
          }

          .line {
            opacity: 0;
          }
        }

        &.activ-tab + li .line {
          opacity: 0;
        }

        i {
          &:hover {
            color: var(--art-text-gray-700);
            background: var(--art-gray-300);
          }
        }
      }
    }
  }

  .right {
    display: flex;

    .btn {
      position: relative;
      top: 0;
      box-sizing: border-box;
      width: 32px;
      height: 32px;
      font-size: 16px;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
      background: var(--art-main-bg-color);
      border-radius: 3px;

      &:hover ul {
        display: inline;
      }

      &.history {
        color: #666;
      }
    }
  }
}

.dark {
  .tabs {
    li {
      i {
        &:hover {
          background: rgb(238 238 238 / 10%) !important;
        }
      }
    }
  }
}

@media only screen and (max-width: $device-ipad) {
  .worktab {
    padding: 6px 10px;
  }
}

@media only screen and (max-width: $device-phone) {
  .worktab {
    padding: 0 15px;
  }
}
